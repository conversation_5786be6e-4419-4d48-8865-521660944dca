<?php

namespace App\Filament\Clusters\AccessControl\Resources;

use App\Actions\User\DeleteUser;
use App\Actions\User\UpdateUser;
use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\AccessControl;
use App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;
use App\Models\User;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Spatie\Permission\Models\Role;
use Throwable;

class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $modelLabel = 'usuário';
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $cluster = AccessControl::class;
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Toggle::make('active')
                    ->label(__('users.forms.fields.active'))
                    ->default(true),
            ]),
            Grid::make(1)->schema([
                Tabs::make()->tabs([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('name')
                                ->label(__('users.forms.fields.name'))
                                ->required()
                                ->columnSpan(3),
                            Select::make('role')
                                ->label(__('users.forms.fields.role'))
                                ->required()
                                ->selectablePlaceholder(false)
                                ->options(
                                    Role::query()
                                        ->orderBy('name')
                                        ->get()
                                        ->pluck('name', 'id')
                                )
                        ]),
                        Grid::make(2)->schema([
                            TextInput::make('email')
                                ->label(__('users.forms.fields.email'))
                                ->required(),
                            TextInput::make('password')
                                ->label(__('users.forms.fields.password')),
                        ]),
                    ]),
                    Tab::make('Arquivos')->schema([
                        Grid::make(1)->schema([
                            TableRepeater::make('user_files')
                                ->relationship('userFiles')
                                ->hiddenLabel()
                                ->defaultItems(0)
                                ->addActionLabel('Adicionar arquivo')
                                ->headers([
                                    Header::make('Arquivo'),
                                ])
                                ->schema([
                                    FileUpload::make('path')
                                        ->label('Arquivo')
                                        ->disk('digitalocean')
                                        ->directory(tenant('id') . '/users')
                                        ->downloadable(),
                                    Hidden::make('provider')
                                        ->default('digitalocean'),
                                ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('users.forms.fields.name')),
                TextColumn::make('email')
                    ->label(__('users.forms.fields.email')),
                TextColumn::make('roles.name')
                    ->label(__('users.forms.fields.role')),
                IconColumn::make('active')
                    ->label(__('users.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('users', 'name'),
                TableTextFilter::buildLike('users', 'email'),
                TableActiveFilter::build('users'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(fn(User $user, array $data): array => array_merge($data, ['role' => $user->roles()->first()->id])),
                    Tables\Actions\EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(fn(User $user, array $data): array => array_merge($data, ['role' => $user->roles()->first()->id]))
                        ->using(function (User $user, array $data): void {
                            try {
                                UpdateUser::run($user, $data);
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->successNotification(success_notification(__('users.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (User $user): void {
                            try {
                                DeleteUser::run($user);
                                success_notification(__('users.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('Ainda sem usuários')
            ->emptyStateDescription('Assim que você cadastrar seus usuários, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageUsers::route('/'),
        ];
    }
}
