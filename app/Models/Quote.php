<?php

namespace App\Models;

use App\Enums\QuoteStatusEnum;
use App\Models\Concerns\Quote\HandlesQuoteAttributes;
use App\Models\Concerns\Quote\HandlesQuoteRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Quote model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  int $protocol_id
 * @property  \Carbon\Carbon $issued_at
 * @property  string $status
 * @property  float $amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_status
 * @property  string $friendly_amount
 * @property  string $friendly_issued_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Protocol $protocol
 *
 * @property  \Illuminate\Support\Collection|\App\Models\QuoteItem[] $quoteItems
 */
class Quote extends Model
{
    use HandlesQuoteAttributes;
    use HandlesQuoteRelationships;

    protected $fillable = [
        'customer_id',
        'protocol_id',
        'issued_at',
        'status',
        'amount',
    ];

    protected $appends = [
        'friendly_status',
        'friendly_amount',
        'friendly_issued_at',
    ];

    protected $casts = [
        'amount' => 'float',
    ];

    public static function booted(): void
    {
        static::creating(function (self $quote): void {
            $quote->status ??= QuoteStatusEnum::Pending->value;
        });
    }
}
