<?php

namespace App\Models\Concerns\Customer;

use App\Models\CustomerAddress;
use App\Models\CustomerContact;
use App\Models\EquipmentCustomer;
use App\Models\ThirdPartyCustomer;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCustomerRelationships
{
    public function customerAddresses(): Has<PERSON><PERSON>
    {
        return $this->hasMany(CustomerAddress::class);
    }

    public function customerContacts(): HasMany
    {
        return $this->hasMany(CustomerContact::class);
    }

    public function customerEquipment(): HasMany
    {
        return $this->hasMany(EquipmentCustomer::class);
    }

    public function thirdPartyCustomers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ThirdPartyCustomer::class);
    }
}
