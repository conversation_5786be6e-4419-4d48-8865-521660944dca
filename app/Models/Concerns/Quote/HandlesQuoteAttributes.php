<?php

namespace App\Models\Concerns\Quote;

use App\Enums\QuoteStatusEnum;

trait HandlesQuoteAttributes
{
    public function getTokenAttribute(): string
    {
        return base64_encode($this->id . ';' . carbon($this->created_at)->format('Y-m-d H:i:s'));
    }

    public function getFriendlyStatusAttribute(): string
    {
        return QuoteStatusEnum::getTranslated()[$this->status];
    }

    public function getFriendlyAmountAttribute(): string
    {
        return format_money($this->amount);
    }

    public function getFriendlyIssuedAtAttribute(): string
    {
        return format_date($this->issued_at);
    }

    public function setAmountAttribute(mixed $value): void
    {
        $this->attributes['amount'] = unmask_money($value);
    }
}
