<?php

namespace App\Models\Concerns\User;

use App\Models\NotificationSetting;
use App\Models\UserFile;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesUserRelationships
{
    public function userFiles(): HasMany
    {
        return $this->hasMany(UserFile::class);
    }

    public function notificationSetting(): HasOne
    {
        return $this->hasOne(NotificationSetting::class);
    }
}
