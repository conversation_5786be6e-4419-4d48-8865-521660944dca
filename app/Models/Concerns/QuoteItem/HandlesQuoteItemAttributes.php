<?php

namespace App\Models\Concerns\QuoteItem;

trait HandlesQuoteItemAttributes
{
    public function getFriendlyUnitAmountAttribute(): string
    {
        return format_money($this->unit_amount);
    }

    public function getFriendlyTotalAmountAttribute(): string
    {
        return format_money($this->total_amount);
    }

    public function setUnitAmountAttribute(mixed $value): void
    {
        $this->attributes['unit_amount'] = unmask_money($value);
    }

    public function setTotalAmountAttribute(mixed $value): void
    {
        $this->attributes['total_amount'] = unmask_money($value);
    }
}
