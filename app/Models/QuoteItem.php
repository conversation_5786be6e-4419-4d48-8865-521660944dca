<?php

namespace App\Models;

use App\Models\Concerns\QuoteItem\HandlesQuoteItemAttributes;
use App\Models\Concerns\QuoteItem\HandlesQuoteItemRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Quote item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $quote_id
 * @property  int $service_order_id
 * @property  int $service_order_execution_checklist_step_id
 * @property  int $service_order_execution_checklist_step_need_id
 * @property  int $equipment_id
 * @property  int $service_type_id
 * @property  int $product_id
 * @property  float $quantity
 * @property  float $unit_amount
 * @property  float $total_amount
 * @property  string $additional_info
 * @property  bool $approved
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_unit_amount
 * @property  string $friendly_total_amount
 *
 * @property  \App\Models\Quote $quote
 * @property  \App\Models\ServiceOrder $serviceOrder
 * @property  \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep
 * @property  \App\Models\ServiceOrderExecutionChecklistStepNeed $serviceOrderExecutionChecklistStepNeed
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\ServiceType $serviceType
 * @property  \App\Models\Product $product
 */
class QuoteItem extends Model
{
    use HandlesQuoteItemAttributes;
    use HandlesQuoteItemRelationships;

    protected $fillable = [
        'quote_id',
        'service_order_id',
        'service_order_execution_checklist_step_id',
        'service_order_execution_checklist_step_need_id',
        'equipment_id',
        'service_type_id',
        'product_id',
        'quantity',
        'unit_amount',
        'total_amount',
        'additional_info',
        'approved',
    ];

    protected $appends = [
        'friendly_unit_amount',
        'friendly_total_amount',
    ];

    protected $casts = [
        'quantity' => 'float',
        'unit_amount' => 'float',
        'total_amount' => 'float',
        'approved' => 'bool',
    ];
}
