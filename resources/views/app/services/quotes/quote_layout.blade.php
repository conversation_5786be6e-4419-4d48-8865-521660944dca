<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>Orçamento</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    {{-- <link href="https://fonts.googleapis.com/css2?family=Inter&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap" rel="stylesheet"> --}}
    <link rel="stylesheet" href="{{ global_asset('css/app/report-default.css') }}">
</head>

<body>
    <div id="report-header" class="mb-2">
        <table class="table table-borderless">
            <tr>
                <td style="width: 35%"><img class="ouess-img-nav" height="45px" src="{{ $logoSrc }}" alt="Logo" /></td>
                <td style="width: 35%; text-align: center; vertical-align: bottom;"><h2>Orçamento</h2></td>
                <td style="width: 30%; text-align: right; vertical-align: bottom;"><p>{{ format_datetime(now()) }}</p></td>
            </tr>
        </table>
    </div>

    <hr>
    <div class="clear-both"></div>

    <div id="report-body">
        <table class="table table-bordered" width="100%">
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 15%">
                    <span><strong>Data emissão</strong></span>
                </td>
                <td colspan="3" style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 35%">
                    <span>{{ $quote->friendly_issued_at }}</span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 15%">
                    <span><strong>Número</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 35%">
                    <span>{{ $quote->id }}</span>
                </td>
            </tr>
        </table>

        <table class="table table-bordered" width="100%">
            <tr>
                <td colspan="6" style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span style="color: white;"><strong>CLIENTE</strong></span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 13%">
                    <span><strong>Razão social</strong></span>
                </td>
                <td colspan="3" style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 55%">
                    <span>{{ $quote->customer->name }}</span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 12%">
                    <span><strong>CPF / CNPJ</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 20%">
                    <span>{{ $quote->customer->friendly_tax_id_number }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 13%">
                    <span><strong>Endereço</strong></span>
                </td>
                <td colspan="5" style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span>{{ $quote->customer->full_address }}</span>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 13%">
                    <span><strong>Telefone</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 17%">
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 13%">
                    <span><strong>Email</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 25%">
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 12%">
                    <span><strong>Vendedor</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem; width: 20%">
                </td>
            </tr>
        </table>

        <table class="table table-striped mt-4" width="100%">
            <tr>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" colspan="6">
                    <span style="color: white;"><strong>ITENS</strong></span>
                </td>
            </tr>
            <tr>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" width="3%">
                    <span style="color: white"><strong>#</strong></span>
                </td>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span style="color: white"><strong>DESCRIÇÃO</strong></span>
                </td>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" width="10%">
                    <span style="color: white"><strong>TIPO</strong></span>
                </td>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" width="7%">
                    <span style="color: white"><strong>QTD.</strong></span>
                </td>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" width="10%">
                    <span style="color: white"><strong>R$ UNIT.</strong></span>
                </td>
                <td style="background-color: rgba(0, 32, 139, 0.75); padding-top: 0.25rem; padding-bottom: 0.25rem;" width="10%">
                    <span style="color: white"><strong>TOTAL</strong></span>
                </td>
            </tr>
            @php
                $index = 1;
            @endphp
            @foreach ($quoteItems as $quoteItem)
                <tr style="margin-top: 4px">
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $index++ }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $quoteItem['equipment_name'] . ' - ' . $quoteItem['service_type_name'] }}</span><br>
                        <span>PI: {{ $quoteItem['service_order_code'] }}</span><br><br>
                        <table class="table table-borderless" width="100%">
                        @foreach ($quoteItem['execution_steps'] as $checklistName => $executionSteps)
                            <span><strong>{{ $checklistName }}</strong></span>
                            <ul>
                                @foreach ($executionSteps as $executionStep)
                                    <li>
                                        <span>{{ $executionStep['name'] }}: </span><span>{!! $executionStep['collected_value'] !!}</span>
                                    </li>
                                @endforeach
                            </ul>
                        @endforeach
                        </table>
                        <span>{!! nl2br($quoteItem['additional_info']) !!}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $quoteItem['type'] }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $quoteItem['quantity'] }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ number_format($quoteItem['unit_amount'], 2, ',', '.') }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ number_format($quoteItem['total_amount'], 2, ',', '.') }}</span>
                    </td>
                </tr>
                @foreach ($quoteItem['needs'] as $need)
                <tr style="margin-top: 4px">
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $index++ }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $need['name'] }}</span><br>
                        <span>PI: {{ $quoteItem['service_order_code'] }}</span><br><br>
                        <span>{!! nl2br($need['additional_info']) !!}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $need['type'] }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ $need['quantity'] }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ number_format($need['unit_amount'], 2, ',', '.') }}</span>
                    </td>
                    <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                        <span>{{ number_format($need['total_amount'], 2, ',', '.') }}</span>
                    </td>
                </tr>
                @endforeach
            @endforeach
            <tr>
                <td colspan="5" style="text-align: right; padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>Total</strong></span>
                </td>
                <td style="padding-top: 0.25rem; padding-bottom: 0.25rem;">
                    <span><strong>{{ number_format($quote->amount, 2, ',', '.') }}</strong></span>
                </td>
            </tr>
        </table>
    </div>
</body>
